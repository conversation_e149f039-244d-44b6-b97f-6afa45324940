<template>
    <view>
        <CustomNavbar :title="'详情'" :titleColor="'##333333'" />
        <view class="header">
            <view class="fifter static-title">养殖场详情</view>
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="main-content" >
            <CommonTable
                :columns="tableColumns"
                :data="tableData"
                @row-click="handleRowClick"
            />
        </view>
        <CommonPagination
                :current="pagination.current"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                @change="handlePageChange"
            />
        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" :pastureId="pastureId" />

    </view>
</template>

<script>
import CustomNavbar from '../components/CustomNavbar.vue'
import filterPopup from './components/filterPopup.vue'
import CommonTable from '../components/CommonTable.vue'
import CommonPagination from '../components/CommonPagination.vue'
import { livestockPage } from '@/api/pages/livestock/farm'
export default {
    name: 'inventoryDetails',
    components: {
        CustomNavbar,
        filterPopup,
        CommonTable,
        CommonPagination
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            filterType: 'inventory',
            pickerFilterShow: false,
            // 页面参数
            pastureId: '', // 养殖场ID
            // 表格列配置
            tableColumns: [
                { title: '耳标号', key: 'earTagNo', width: '120rpx', align: 'center' },
                { title: '品种', key: 'varietiesName', width: '120rpx', align: 'center' },
                { title: '栏位', key: 'penName', width: '100rpx', align: 'center' },
                { title: '体重（kg）', key: 'livestockWeight', width: '140rpx', align: 'center' },
                { title: '入库批次', key: 'batch', width: '140rpx', align: 'center' },
                { title: '健康状况', key: 'healthStatusText', width: '120rpx', align: 'center' }
            ],
            tableData: [],
            // 分页配置
            pagination: {
                current: 1,
                pageSize: 6,
                total: 0
            },
            // 筛选参数
            filterParams: {},
            // 加载状态
            loading: false
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        }
    },

    onLoad(options) {
        if (options.pastureId) {
            this.pastureId = options.pastureId;
        }
        this.loadTableData();
    },

    onReady() {
        if (this.tableData.length === 0) {
            this.loadTableData();
        }
    },

    methods: {
        resetSearch() {
            console.log('resetSearch');
            this.filterParams = {};
            this.pagination.current = 1;
            this.loadTableData();
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
            this.pagination.current = 1;
            this.loadTableData();
        },
        fifterClick(){
            this.pickerFilterShow = true;
        },
        async loadTableData() {
            this.loading = true;
            try {
                const params = {
                    ...this.filterParams,
                    pastureId: this.pastureId,
                    pageNum: this.pagination.current,
                    pageSize: this.pagination.pageSize
                };
                const res = await livestockPage(params);

                if (res.code === 200 && res.result) {
                    const processedData = (res.result.list || []).map(item => ({
                        ...item,
                        healthStatusText: this.getHealthStatusText(item.healthStatus),
                        livestockWeight: item.livestockWeight ? parseFloat(item.livestockWeight).toFixed(1) : '0.0'
                    }));

                    this.tableData = processedData;
                    this.pagination.total = parseInt(res.result.total) || 0;
                } else {
                    throw new Error(res.message || '获取数据失败');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.tableData = [];
                this.pagination.total = 0;
            } finally {
                this.loading = false;
            }
        },

        getHealthStatusText(status) {
            const statusMap = {
                1: '健康',
                2: '亚健康',
                3: '生病',
                0: '未知'
            };
            return statusMap[status] || '未知';
        },
        handlePageChange(page) {
            this.pagination.current = page;
            this.loadTableData();
        },
        // 表格行点击
        handleRowClick(row, index) {
            console.log('点击行:', row, index);
        }
    }
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 250rpx;
    display: flex;
    padding-top: 120rpx;
    box-sizing: border-box;
    position: relative;
    position: relative;
    .static-title {
        left: 30rpx;
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
    }
}

.fifter {
    position: absolute;
    top: 197rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.main-content {
    background: #fff;
    // height: 980rpx;
    margin: 10rpx 30rpx 50rpx 30rpx;
    border-radius: 30rpx;
    padding: 0 20rpx 40rpx 20rpx;
    overflow: auto;
    /deep/ .common-table{
        margin-top: 20rpx;
    }

    :deep(.common-pagination) {
        background: transparent;
        margin-top: 20rpx;
    }
}
</style>
