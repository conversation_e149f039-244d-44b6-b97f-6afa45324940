<template>
    <view class="common-pagination" v-if="total > 0">
        <view class="pagination-controls">
            <!-- 上一页 -->
            <view class="page-btn prev-btn" :class="{ disabled: !canPrevGroup }" @click="handlePrevPage">
                <text>‹</text>
            </view>

            <!-- 页码 -->
            <view class="page-numbers">
                <view v-for="page in visiblePages" :key="`page-${currentGroup}-${page}`" class="page-btn page-number"
                    :class="{ active: page === currentPage }" @click="handlePageClick(page)">
                    <text>{{ page }}</text>
                </view>
            </view>

            <!-- 下一页 -->
            <view class="page-btn next-btn" :class="{ disabled: !canNextGroup }" @click="handleNextPage">
                <text>›</text>
            </view>
        </view>
        <view class="total-info">
            <text>共{{ total }}条</text>
        </view>
    </view>
</template>

<script>
export default {
    name: 'CommonPagination',
    props: {
        // 当前页码
        current: {
            type: Number,
            default: 1
        },
        // 每页条数
        pageSize: {
            type: Number,
            default: 10
        },
        // 总条数
        total: {
            type: Number,
            default: 0
        },
        // 显示的页码按钮数量
        showPageCount: {
            type: Number,
            default: 5
        }
    },
    data() {
        return {
            currentPage: this.current || 1,
            currentGroup: Math.max(1, Math.ceil((this.current || 1) / this.showPageCount)) // 当前页码组，至少为1
        }
    },
    computed: {
        // 总页数
        totalPages() {
            if (this.total <= 0 || this.pageSize <= 0) {
                return 0;
            }
            return Math.ceil(this.total / this.pageSize);
        },
        // 可见的页码数组
        visiblePages() {
            const total = this.totalPages;
            const showCount = this.showPageCount;

            if (total <= 0) {
                return [];
            }

            if (total <= showCount) {
                return Array.from({ length: total }, (_, i) => i + 1);
            }

            const start = (this.currentGroup - 1) * showCount + 1;
            const end = Math.min(start + showCount - 1, total);

            const pages = [];
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            return pages;
        },
        // 总页码组数
        totalGroups() {
            if (this.totalPages <= 0) {
                return 0;
            }
            return Math.ceil(this.totalPages / this.showPageCount);
        },
        // 是否可以向左切换页码组
        canPrevGroup() {
            return this.currentGroup > 1;
        },
        // 是否可以向右切换页码组
        canNextGroup() {
            return this.currentGroup < this.totalGroups;
        }
    },
    watch: {
        current(newVal) {
            if (newVal && newVal !== this.currentPage) {
                this.currentPage = newVal;
                const newGroup = Math.max(1, Math.ceil(newVal / this.showPageCount));
                if (newGroup !== this.currentGroup) {
                    this.currentGroup = newGroup;
                }
            }
        },
        // 监听总数变化，确保当前页不超出范围
        total(newVal) {
            if (newVal !== undefined && this.pageSize > 0) {
                const maxPages = Math.ceil(newVal / this.pageSize);
                if (maxPages > 0 && this.currentPage > maxPages) {
                    this.currentPage = maxPages;
                    const newGroup = Math.max(1, Math.ceil(this.currentPage / this.showPageCount));
                    if (newGroup !== this.currentGroup) {
                        this.currentGroup = newGroup;
                    }
                }
            }
        }
    },
    methods: {
        // 检查页码是否在当前页码组范围内
        isPageInCurrentGroup(page) {
            const start = (this.currentGroup - 1) * this.showPageCount + 1;
            const end = Math.min(start + this.showPageCount - 1, this.totalPages);
            return page >= start && page <= end;
        },
        // 上一页
        handlePrevPage() {
            if (this.canPrevGroup) {
                this.currentGroup--;
                // 切换到新页码组的第一页
                const newPage = (this.currentGroup - 1) * this.showPageCount + 1;
                this.changePage(newPage);
            }
        },
        // 下一页
        handleNextPage() {
            if (this.canNextGroup) {
                this.currentGroup++;
                // 切换到新页码组的第一页
                const newPage = (this.currentGroup - 1) * this.showPageCount + 1;
                this.changePage(newPage);
            }
        },
        // 页码点击
        handlePageClick(page) {
            console.log('📄 Page click:', page, 'current:', this.currentPage);
            if (page !== this.currentPage) {
                this.currentPage = page;
                console.log('✅ Emitting change event with page:', page);
                this.$emit('change', page);
            }
        },
        // 切换页码
        changePage(page) {
            if (page !== this.currentPage) {
                this.currentPage = page;
                this.$emit('change', page);
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.common-pagination {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 0 40rpx 0;
    margin: 0 30rpx 30rpx 30rpx;

    .total-info {
        margin-top: 30rpx;

        text {
            font-size: 26rpx;
            color: linear-gradient(153deg, #67D46F 0%, #24C570 100%);
            ;
        }
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .page-btn {
            min-width: 50rpx;
            height: 50rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dcdfe6;
            border-radius: 8rpx;
            background: #fff;
            cursor: pointer;
            // transition: all 0.3s;

            text {
                font-size: 28rpx;
                color: #606266;
            }

            &:hover:not(.disabled) {
                border-color: linear-gradient(153deg, #67D46F 0%, #24C570 100%);
                ;
                color: linear-gradient(153deg, #67D46F 0%, #24C570 100%);
                ;

                text {
                    color: linear-gradient(153deg, #67D46F 0%, #24C570 100%);
                    ;
                }
            }

            &.active {
                background: linear-gradient(153deg, #67D46F 0%, #24C570 100%);
                ;
                border-color: linear-gradient(153deg, #67D46F 0%, #24C570 100%);
                ;

                text {
                    color: #fff;
                }
            }

            &.disabled {
                background: #f5f7fa;
                border-color: #e4e7ed;
                cursor: not-allowed;

                text {
                    color: #c0c4cc;
                }
            }


        }

        .page-numbers {
            display: flex;
            align-items: center;
            gap: 16rpx;
        }

        .prev-btn,
        .next-btn {
            width: 50rpx;

            text {
                font-size: 28rpx;
                font-weight: bold;
            }
        }
    }
}
</style>
